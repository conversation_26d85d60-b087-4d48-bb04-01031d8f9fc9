// src/components/auth/auth-modal.tsx
'use client';

import { useEffect, useState } from 'react';
import AuthForm from './auth-form';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'login' | 'signup';
}

const AuthModal = ({ isOpen, onClose, defaultTab = 'signup' }: AuthModalProps) => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab, isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };  return (
    <div className="fixed inset-0 bg-gradient-to-br from-slate-900/80 via-slate-800/80 to-slate-900/80 flex items-center justify-center p-4 z-50 animate-fadeIn font-sans backdrop-blur-md">
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl w-full max-w-md mx-auto relative animate-slideIn max-h-[90vh] overflow-hidden shadow-2xl border border-white/20 ring-1 ring-slate-200/50 sm:max-w-lg md:max-w-md">        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-600 transition-all duration-200 p-2 z-10 hover:bg-slate-100/80 rounded-full backdrop-blur-sm"
          aria-label="Close modal"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>{/* Modal Header - Modern */}
        <div className="px-6 pt-6 pb-4 bg-gradient-to-br from-white/95 to-slate-50/95 backdrop-blur-sm border-b border-slate-200/50">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2.5 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-700 rounded-lg flex items-center justify-center shadow-lg shadow-indigo-500/25">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                </svg>
              </div>
              <h2 className="text-xl-app font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">EduPro</h2>
            </div>
            
            {/* Tab Toggle - Enhanced */}
            <div className="flex bg-slate-100/80 backdrop-blur-sm rounded-xl p-1 shadow-inner">
              <button
                onClick={() => handleToggle('signup')}
                type="button"
                className={`flex-1 py-3 px-4 rounded-lg text-sm-app font-semibold transition-all duration-300 ${
                  activeTab === 'signup'
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-[1.02]'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/50'
                }`}
              >
                Sign Up
              </button>
              <button
                onClick={() => handleToggle('login')}
                type="button"
                className={`flex-1 py-3 px-4 rounded-lg text-sm-app font-semibold transition-all duration-300 ${
                  activeTab === 'login'
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-[1.02]'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/50'
                }`}
              >
                Sign In
              </button>
            </div>
          </div>
        </div>        {/* Modal Body - Enhanced with Pastel Gradient */}
        <div className="px-6 py-4 max-h-[calc(70vh-120px)] overflow-y-auto bg-gradient-to-br from-indigo-50/80 via-purple-50/60 to-pink-50/80 backdrop-blur-sm relative">
          {/* Subtle overlay pattern */}
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/30 via-transparent to-violet-50/30 pointer-events-none"></div>
          <div className="relative z-10">
            <div className="text-center mb-4">
              <h3 className="text-xl-app font-bold text-slate-900 mb-1">
                {activeTab === 'signup' ? 'Create Your Account' : 'Welcome Back'}
              </h3>
              <p className="text-slate-600 text-sm-app leading-relaxed">
                {activeTab === 'signup'
                  ? 'Join thousands of educators worldwide and unlock premium features'
                  : 'Sign in to continue your learning journey with EduPro'
                }
              </p>
            </div>
            <AuthForm formType={activeTab} onToggleForm={handleToggle} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
