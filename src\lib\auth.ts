// src/lib/auth.ts
'use client';

import { SupabaseAuthService } from './supabase-auth';

export interface User {
  name: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

export const getAuthState = (): User => {
  if (typeof window === 'undefined') {
    return { name: '', email: '', role: '', isAuthenticated: false };
  }

  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const userName = localStorage.getItem('userName') || '';
  const userEmail = localStorage.getItem('userEmail') || '';
  const userRole = localStorage.getItem('userRole') || '';

  return {
    name: userName,
    email: userEmail,
    role: userRole,
    isAuthenticated,
  };
};

export const logout = async () => {
  if (typeof window !== 'undefined') {
    // Sign out from Supabase
    await SupabaseAuthService.signOut();
    
    // Redirect to product page
    window.location.href = '/product';
  }
};

export const requireAuth = async () => {
  if (typeof window !== 'undefined') {
    // First check localStorage for quick response
    const localAuth = getAuthState();
    if (!localAuth.isAuthenticated) {
      window.location.href = '/product';
      return false;
    }

    // Then verify with Supabase session
    try {
      const currentUser = await SupabaseAuthService.getCurrentUser();
      if (!currentUser) {
        window.location.href = '/product';
        return false;
      }
      return true;
    } catch (error) {
      console.error('Auth verification failed:', error);
      // If Supabase check fails, fall back to localStorage
      return localAuth.isAuthenticated;
    }
  }
  return false;
};

// New function to initialize auth state from Supabase
export const initializeAuth = async (): Promise<User | null> => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const currentUser = await SupabaseAuthService.getCurrentUser();
    return currentUser;
  } catch (error) {
    console.error('Failed to initialize auth:', error);
    return null;
  }
};
