// src/services/core/databaseService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '../../lib/supabase';
import { Database } from '../../types/database';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, ServiceError } from './errorHandler';

/**
 * Database service abstraction layer
 * Provides a clean interface for database operations with error handling
 */
export class DatabaseService {
  private static instance: DatabaseService;
  private client: SupabaseClient<Database>;

  private constructor(client: SupabaseClient<Database>) {
    this.client = client;
  }

  /**
   * Get singleton instance of DatabaseService
   */
  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService(supabase);
    }
    return DatabaseService.instance;
  }

  /**
   * Get the underlying Supabase client
   */
  getClient(): SupabaseClient<Database> {
    return this.client;
  }

  /**
   * Execute a query with error handling
   */
  async executeQuery<T>(
    operation: (client: SupabaseClient<Database>) => Promise<{ data: T | null; error: any }>,
    context: string
  ): Promise<T> {
    try {
      const { data, error } = await operation(this.client);
      
      if (error) {
        throw ErrorHandler.handle(error, context);
      }

      if (data === null) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          `No data returned for ${context}`
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      throw ErrorHandler.handle(error, context);
    }
  }

  /**
   * Execute a query that may return null
   */
  async executeQueryOptional<T>(
    operation: (client: SupabaseClient<Database>) => Promise<{ data: T | null; error: any }>,
    context: string
  ): Promise<T | null> {
    try {
      const { data, error } = await operation(this.client);
      
      if (error) {
        // Handle "not found" errors gracefully
        if (error.code === 'PGRST116') {
          return null;
        }
        throw ErrorHandler.handle(error, context);
      }

      return data;
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      throw ErrorHandler.handle(error, context);
    }
  }

  /**
   * Execute multiple operations in sequence with rollback capability
   */
  async executeTransaction<T>(
    operations: ((client: SupabaseClient<Database>) => Promise<T>)[],
    context: string
  ): Promise<T[]> {
    const results: T[] = [];
    const rollbackOperations: (() => Promise<void>)[] = [];

    try {
      for (const operation of operations) {
        const result = await operation(this.client);
        results.push(result);
      }

      return results;
    } catch (error) {
      // Attempt to rollback completed operations
      console.warn(`Transaction failed for ${context}, attempting rollback...`);
      
      for (const rollback of rollbackOperations.reverse()) {
        try {
          await rollback();
        } catch (rollbackError) {
          console.error('Rollback operation failed:', rollbackError);
        }
      }

      throw ErrorHandler.handle(error, `Transaction failed: ${context}`);
    }
  }

  /**
   * Check if a record exists
   */
  async exists(tableName: string, id: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(tableName as any)
        .select('id')
        .eq('id', id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, `Check existence in ${tableName}`);
      }

      return data !== null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Check if a value is unique in a table
   */
  async isUnique(
    tableName: string, 
    field: string, 
    value: any, 
    excludeId?: string
  ): Promise<boolean> {
    try {
      let query = this.client
        .from(tableName as any)
        .select('id')
        .eq(field, value);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, `Check uniqueness in ${tableName}.${field}`);
      }

      return data === null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      throw error;
    }
  }

  /**
   * Get count of records matching criteria
   */
  async count(
    tableName: string, 
    filters?: Record<string, any>
  ): Promise<number> {
    try {
      let query = this.client
        .from(tableName as any)
        .select('*', { count: 'exact', head: true });

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        throw ErrorHandler.handle(error, `Count records in ${tableName}`);
      }

      return count || 0;
    } catch (error) {
      throw ErrorHandler.handle(error, `Count records in ${tableName}`);
    }
  }

  /**
   * Batch insert records
   */
  async batchInsert<T>(
    tableName: string,
    records: any[],
    batchSize: number = 100
  ): Promise<T[]> {
    const results: T[] = [];
    
    try {
      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);
        
        const { data, error } = await this.client
          .from(tableName as any)
          .insert(batch)
          .select();

        if (error) {
          throw ErrorHandler.handle(error, `Batch insert into ${tableName}`);
        }

        if (data) {
          results.push(...data);
        }
      }

      return results;
    } catch (error) {
      throw ErrorHandler.handle(error, `Batch insert into ${tableName}`);
    }
  }

  /**
   * Soft delete a record (set is_active = false)
   */
  async softDelete(tableName: string, id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(tableName as any)
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) {
        throw ErrorHandler.handle(error, `Soft delete from ${tableName}`);
      }
    } catch (error) {
      throw ErrorHandler.handle(error, `Soft delete from ${tableName}`);
    }
  }

  /**
   * Hard delete a record
   */
  async hardDelete(tableName: string, id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(tableName as any)
        .delete()
        .eq('id', id);

      if (error) {
        throw ErrorHandler.handle(error, `Hard delete from ${tableName}`);
      }
    } catch (error) {
      throw ErrorHandler.handle(error, `Hard delete from ${tableName}`);
    }
  }

  /**
   * Execute raw SQL query (use with caution)
   */
  async executeRawQuery<T>(
    query: string,
    params?: any[]
  ): Promise<T> {
    try {
      const { data, error } = await this.client.rpc('execute_sql', {
        query,
        params: params || []
      });

      if (error) {
        throw ErrorHandler.handle(error, 'Execute raw SQL');
      }

      return data;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Execute raw SQL');
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'unhealthy';
    latency: number;
    timestamp: string;
  }> {
    const startTime = Date.now();
    
    try {
      await this.client.from('profiles').select('id').limit(1);
      
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        latency,
        timestamp: new Date().toISOString()
      };
    } catch {
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Create a backup of critical data
   */
  async createBackup(tables: string[]): Promise<Record<string, any[]>> {
    const backup: Record<string, any[]> = {};
    
    try {
      for (const table of tables) {
        const { data, error } = await this.client
          .from(table as any)
          .select('*');

        if (error) {
          throw ErrorHandler.handle(error, `Backup table ${table}`);
        }

        backup[table] = data || [];
      }

      return backup;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Create backup');
    }
  }
}

/**
 * Database connection factory
 */
export class DatabaseConnectionFactory {
  /**
   * Create a new database service instance
   */
  static create(client?: SupabaseClient<Database>): DatabaseService {
    if (client) {
      return new (DatabaseService as any)(client);
    }
    return DatabaseService.getInstance();
  }

  /**
   * Test database connection
   */
  static async testConnection(client?: SupabaseClient<Database>): Promise<boolean> {
    try {
      const dbService = client ? 
        new (DatabaseService as any)(client) : 
        DatabaseService.getInstance();
      
      const health = await dbService.getHealthStatus();
      return health.status === 'healthy';
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
