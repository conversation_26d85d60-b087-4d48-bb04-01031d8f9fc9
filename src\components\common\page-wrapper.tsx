// src/components/common/page-wrapper.tsx
'use client';

import React from 'react';

interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
}

const PageWrapper = ({ children, className = 'p-2 bg-gray-50 min-h-full border-l border-gray-200 shadow-sm' }: PageWrapperProps) => {
  return (
    <div className={className}>
      {children}
    </div>
  );
};

export default PageWrapper;
