// src/lib/supabase-auth.ts
'use client';

import type { User } from './auth';
import { supabase } from './supabase';

export interface SupabaseAuthResponse {
  user: User | null;
  error: string | null;
  success: boolean;
}

export class SupabaseAuthService {
  /**
   * Sign up a new user with Supabase
   */
  static async signUp(email: string, password: string, userData: {
    name: string;
    role: string;
  }): Promise<SupabaseAuthResponse> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.name,
            role: userData.role
          }
        }
      });

      if (error) {
        return {
          user: null,
          error: error.message,
          success: false
        };
      }

      if (data.user) {        // Create profile in the profiles table
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email || '',
            full_name: userData.name,
            role: userData.role as 'student' | 'teacher' | 'admin' | 'parent'
          });

        if (profileError) {
          console.warn('Profile creation failed:', profileError);
        }

        const user: User = {
          name: userData.name,
          email: data.user.email || '',
          role: userData.role,
          isAuthenticated: true
        };

        // Store in localStorage for compatibility
        this.storeUserLocally(user);

        return {
          user,
          error: null,
          success: true
        };
      }

      return {
        user: null,
        error: 'User creation failed',
        success: false
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign in an existing user with Supabase
   */
  static async signIn(email: string, password: string): Promise<SupabaseAuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return {
          user: null,
          error: error.message,
          success: false
        };
      }

      if (data.user) {        // Get user profile from database
        const { data: profile, error: _profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();

        const userName = profile?.full_name || data.user.user_metadata?.full_name || data.user.email?.split('@')[0] || '';
        const userRole = profile?.role || data.user.user_metadata?.role || 'student';

        const user: User = {
          name: userName,
          email: data.user.email || '',
          role: userRole,
          isAuthenticated: true
        };

        // Store in localStorage for compatibility
        this.storeUserLocally(user);

        return {
          user,
          error: null,
          success: true
        };
      }

      return {
        user: null,
        error: 'Sign in failed',
        success: false
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign in with Google OAuth
   */  static async signInWithGoogle(): Promise<SupabaseAuthResponse> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) {
        return {
          user: null,
          error: error.message,
          success: false
        };
      }

      return {
        user: null,
        error: null,
        success: true
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign in with Facebook OAuth
   */  static async signInWithFacebook(): Promise<SupabaseAuthResponse> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'facebook',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) {
        return {
          user: null,
          error: error.message,
          success: false
        };
      }

      return {
        user: null,
        error: null,
        success: true
      };
    } catch (error) {
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false
      };
    }
  }

  /**
   * Sign out the current user
   */
  static async signOut(): Promise<void> {
    try {
      await supabase.auth.signOut();
      this.clearUserLocally();
    } catch (error) {
      console.error('Sign out error:', error);
      // Clear local storage even if Supabase signOut fails
      this.clearUserLocally();
    }
  }

  /**
   * Get the current authenticated user
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        return null;
      }

      // Get user profile from database
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      const userName = profile?.full_name || user.user_metadata?.full_name || user.email?.split('@')[0] || '';
      const userRole = profile?.role || user.user_metadata?.role || 'student';      const authUser: User = {
        name: userName,
        email: user.email || '',
        role: userRole,
        isAuthenticated: true
      };

      // Store in localStorage for compatibility
      this.storeUserLocally(authUser);

      return authUser;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Check if user session exists
   */
  static async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      return { session, error };
    } catch (error) {
      console.error('Get session error:', error);
      return { session: null, error };
    }
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const user = await this.getCurrentUser();
        callback(user);
      } else {
        this.clearUserLocally();
        callback(null);
      }
    });
  }

  /**
   * Store user data in localStorage for compatibility with existing code
   */
  private static storeUserLocally(user: User): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('userName', user.name);
      localStorage.setItem('userEmail', user.email);
      localStorage.setItem('userRole', user.role);
    }
  }

  /**
   * Clear user data from localStorage
   */
  private static clearUserLocally(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('userName');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userRole');
    }
  }
}
