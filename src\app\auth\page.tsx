'use client';

import { useState } from 'react';
import AuthSection from '../../components/auth/auth-section';
import LandingFooter from '../../components/landing/landing-footer';
import LandingHeader from '../../components/landing/landing-header';

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };

  const handleOpenAuthModal = () => {
    // This function is not needed for the auth page since we're already on the auth page
    // But we need to provide it for the LandingHeader component
  };

  return (
    <div className="min-h-screen flex flex-col bg-slate-50 text-slate-900 dark:bg-slate-900 dark:text-slate-100">
      <LandingHeader onOpenAuthModal={handleOpenAuthModal} />
      
      <main className="flex-grow flex items-center">
        <AuthSection activeTab={activeTab} onToggleForm={handleToggle} />
      </main>
      <LandingFooter />
    </div>
  );
}
