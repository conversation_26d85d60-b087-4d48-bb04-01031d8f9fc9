'use client';

import { useState } from 'react';
import AuthModal from '../../components/auth/auth-modal';
import LandingFooter from '../../components/landing/landing-footer';
import LandingHeader from '../../components/landing/landing-header';

export default function ProductPage() {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleOpenAuthModal = () => {
    setIsAuthModalOpen(true);
  };

  const handleCloseAuthModal = () => {
    setIsAuthModalOpen(false);
  };
  return (
    <div className="min-h-screen flex flex-col bg-white text-slate-900 font-sans">      <LandingHeader onOpenAuthModal={handleOpenAuthModal} />
      <main className="flex-grow">        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-indigo-50 via-white to-purple-50 min-h-[60vh] max-h-[75vh] flex items-center overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
          </div>
          
          <div className="container mx-auto px-4 relative z-10 py-12">
            <div className="text-center max-w-5xl mx-auto">
              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 text-xs-app font-medium mb-8 animate-fadeInUp">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                </svg>
                Trusted by 10,000+ Schools Worldwide
              </div>
                <h1 className="text-responsive-4xl font-bold mb-6 animate-fadeInUp">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Transform
                </span>
                <br />
                <span className="text-slate-800">Your School</span>
                <br />
                <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
                  Experience
                </span>
              </h1>

              <p className="text-responsive-lg text-slate-600 mb-8 leading-relaxed max-w-3xl mx-auto animate-fadeInUp" style={{animationDelay: '0.2s'}}>
                Streamline operations, enhance learning outcomes, and build stronger school communities with our
                <span className="font-semibold text-indigo-600"> AI-powered platform</span>.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-fadeInUp" style={{animationDelay: '0.4s'}}>                <button
                  onClick={handleOpenAuthModal}
                  className="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold text-lg-app shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                >
                  <span className="relative z-10">Start Free Trial</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
                <button
                  onClick={() => document.getElementById('product-info')?.scrollIntoView({behavior: 'smooth'})}
                  className="px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-2xl font-semibold text-lg-app hover:border-indigo-500 hover:text-indigo-600 transition-all duration-300 transform hover:-translate-y-1 bg-white/80 backdrop-blur-sm"
                >
                  <span className="flex items-center">
                    Watch Demo
                    <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1m-6 4h1m4 0h1"></path>
                    </svg>
                  </span>
                </button>
              </div>
              
              {/* Trust Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto animate-fadeInUp" style={{animationDelay: '0.6s'}}>
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm-app">14-Day Free Trial</div>
                    <div className="text-xs-app text-slate-600">No credit card required</div>
                  </div>
                </div>
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm-app">FERPA Compliant</div>
                    <div className="text-xs-app text-slate-600">Enterprise security</div>
                  </div>
                </div>
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm-app">24/7 Support</div>
                    <div className="text-xs-app text-slate-600">Expert assistance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>        {/* Why Schools Choose EduPro Section */}
        <section id="product-info" className="py-16 md:py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-indigo-100 text-indigo-800 text-xs-app font-medium mb-4">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Why Schools Choose EduPro
              </div>              <h2 className="text-responsive-2xl font-bold mb-6 text-slate-800">
                Comprehensive Platform, Proven Results
              </h2>
              <p className="text-responsive-base text-slate-600 max-w-2xl mx-auto">
                Transform your educational institution with enterprise-grade features designed for modern schools.
              </p>
            </div>
            
            {/* Feature List Layout */}
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                {/* Left Column */}
                <div className="space-y-8">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>                    <div>
                      <h3 className="text-lg-app font-semibold text-slate-800 mb-2">Complete Student Management</h3>
                      <p className="text-slate-600 leading-relaxed text-base-app">
                        Centralized student profiles with enrollment tracking, academic progress, and behavioral insights.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg-app font-semibold text-slate-800 mb-2">AI-Powered Analytics</h3>
                      <p className="text-slate-600 leading-relaxed text-base-app">
                        Real-time insights with predictive modeling and automated report generation for data-driven decisions.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg-app font-semibold text-slate-800 mb-2">Unified Communication</h3>
                      <p className="text-slate-600 leading-relaxed text-base-app">
                        Connect teachers, students, and parents with real-time messaging and multilingual support.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="space-y-8">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-800 mb-2">Intelligent Scheduling</h3>
                      <p className="text-slate-600 leading-relaxed">
                        AI-optimized timetables with automatic conflict resolution and dynamic scheduling adjustments.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-800 mb-2">Enterprise Security</h3>
                      <p className="text-slate-600 leading-relaxed">
                        Bank-level security with FERPA compliance, SSO integration, and comprehensive audit trails.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-800 mb-2">Mobile-First Platform</h3>
                      <p className="text-slate-600 leading-relaxed">
                        Native mobile apps with offline capabilities, push notifications, and seamless cloud sync.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats Row */}
              <div className="border-t border-slate-200 pt-12">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                  <div>
                    <div className="text-3xl font-bold text-indigo-600 mb-2">10,000+</div>
                    <div className="text-sm text-slate-600">Schools Trust EduPro</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 mb-2">99.9%</div>
                    <div className="text-sm text-slate-600">System Uptime</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                    <div className="text-sm text-slate-600">Expert Support</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-orange-600 mb-2">50+</div>
                    <div className="text-sm text-slate-600">Countries Worldwide</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>        {/* Final CTA Section */}
        <section className="py-16 md:py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
          <div className="container mx-auto px-4 text-center text-white">
            <h3 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your School?
            </h3>
            <p className="text-indigo-100 text-xl mb-8 max-w-3xl mx-auto leading-relaxed">
              Join thousands of educational institutions worldwide that trust EduPro to streamline their operations and enhance learning outcomes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={handleOpenAuthModal}
                className="bg-white text-indigo-600 px-10 py-4 rounded-2xl font-semibold text-lg hover:bg-gray-50 transition duration-200 shadow-2xl transform hover:-translate-y-1"
              >
                Start Free Trial
              </button>
              <button 
                onClick={handleOpenAuthModal}
                className="border-2 border-white text-white px-10 py-4 rounded-2xl font-semibold text-lg hover:bg-white hover:text-indigo-600 transition duration-200 transform hover:-translate-y-1"
              >
                Request Demo
              </button>
            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
      
      {/* Auth Modal */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={handleCloseAuthModal}
        defaultTab="signup"
      />
    </div>
  );
}
